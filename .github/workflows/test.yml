name: Test PDF Question Extraction System

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10"]

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run setup verification
      run: |
        python test_setup.py
      env:
        SAMBANOVA_API_KEY: ${{ secrets.SAMBANOVA_API_KEY }}
        MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
        QDRANT_URL: ${{ secrets.QDRANT_URL }}
        QDRANT_API_KEY: ${{ secrets.QDRANT_API_KEY }}
        COHERE_API_KEY: ${{ secrets.COHERE_API_KEY }}
    
    - name: Test configuration loading
      run: |
        python -c "from config import API_CONFIG; print('Config loaded successfully')"
      env:
        SAMBANOVA_API_KEY: "test_key"
        MISTRAL_API_KEY: "test_key"
        QDRANT_URL: "test_url"
        QDRANT_API_KEY: "test_key"
        COHERE_API_KEY: "test_key"
