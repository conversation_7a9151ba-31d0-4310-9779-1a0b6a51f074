{"total_questions": 29, "extraction_timestamp": 1748292440.2412856, "questions": [{"question_id": "1", "question_text": "Which movie features the song \"Jai Ho\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON>", "B) Slumdog Millionaire", "C) <PERSON>b <PERSON>", "D) 3 Idiots"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": "**Answer: B (The song \"<PERSON> Ho\" is featured in the movie \"Slumdog Millionaire,\" composed by <PERSON><PERSON><PERSON><PERSON>. The context mentions that <PERSON><PERSON><PERSON><PERSON> is the music director for the movie <PERSON><PERSON><PERSON><PERSON>, but \"Jai Ho\" is not mentioned in relation to <PERSON><PERSON><PERSON><PERSON>. \"Jai Ho\" gained global recognition and won an Academy Award for Best Original Song in 2009 for \"Slumdog Millionaire.\")**", "context_used": 5}, {"question_id": "2", "question_text": "Who directed the film \"<PERSON>ng <PERSON>\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON>", "B) <PERSON><PERSON><PERSON>", "C) <PERSON><PERSON><PERSON>", "D) <PERSON><PERSON>"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": "Answer: B (The context explicitly states that the director of \"Rang De Basanti\" is <PERSON><PERSON><PERSON>.)", "context_used": 5}, {"question_id": "3", "question_text": "Which actor played the lead role in the movie \"Ghajin<PERSON>\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON>", "B) <PERSON><PERSON><PERSON>", "C) <PERSON><PERSON>", "D) <PERSON><PERSON><PERSON>"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": "Answer: B (The context specifies that <PERSON><PERSON><PERSON> played the lead role of <PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON> in the movie \"<PERSON>hajin<PERSON>\".)", "context_used": 5}, {"question_id": "4", "question_text": "Which film features the song \"Mauja Hi Mauja\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON>", "B) <PERSON> <PERSON><PERSON>", "C) Bach<PERSON>", "D) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": "Answer: C (The song \"Mauja Hi Mauja\" is from the film \"Bachna Ae <PERSON>eeno,\" which was released in 2008 and stars <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.)\n\nExplanation:\nWhile the provided context does not explicitly list the film \"Bachna Ae Haseeno\" or the song \"Mauja Hi Mauja,\" the context does mention key albums and films from the 2000-2010 period. The song \"Mauja Hi Mauja\" is a popular track from the 2008 film \"Bachna Ae Haseeno,\" which fits within the specified time frame. Given the options, \"Bachna Ae Haseeno\" is the correct choice.", "context_used": 5}, {"question_id": "5", "question_text": "Which movie is known for the dialogue \"Mere paas maa hai\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON>", "B) <PERSON><PERSON><PERSON><PERSON>", "C) Waqt", "D) The dialogue is not from the 2000-2010 decade."], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": "Answer: D (The dialogue \"Mere paas maa hai\" is from the movie *<PERSON><PERSON>*, which was released in 1975, not within the 2000-2010 decade.)", "context_used": 5}, {"question_id": "6", "question_text": "Which film marked the debut of <PERSON><PERSON><PERSON>?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON><PERSON>", "B) Wake Up Sid", "C) <PERSON><PERSON><PERSON>", "D) <PERSON>: Salesman of the Year"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": "Answer: A (The context mentions that <PERSON><PERSON><PERSON> debuted with *<PERSON><PERSON><PERSON>* in 2007.)", "context_used": 5}, {"question_id": "7", "question_text": "Which movie features the character \"<PERSON>\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON> M.B.B.S.", "B) Lage <PERSON>", "C) 3 Idiots", "D) PK"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": "Answer: B (The character \"<PERSON>\" is featured in \"Lage Raho <PERSON>,\" played by <PERSON><PERSON><PERSON>. He is a notable sidekick to <PERSON><PERSON>, played by <PERSON><PERSON>.)", "context_used": 1}, {"question_id": "8", "question_text": "Which actress played the lead role in \"Fashion\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON><PERSON>", "B) <PERSON>", "C) <PERSON><PERSON><PERSON>", "D) <PERSON><PERSON>"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": "Answer: A (<PERSON><PERSON><PERSON> played the lead role of <PERSON><PERSON><PERSON> in \"Fashion\")", "context_used": 5}, {"question_id": "1", "question_text": "Which of the following movies starred <PERSON><PERSON><PERSON><PERSON> in the 2000-2010 decade?", "question_type": "multiple_choice_multi", "options": ["A) Black", "B) Sarkar", "C) Paa", "D) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": "Answers: A, B, C (<PERSON><PERSON><PERSON><PERSON> starred in *<PERSON>* (2005), *<PERSON><PERSON><PERSON>* (2005), and *<PERSON><PERSON>* (2009) within the 2000-2010 decade, as indicated in the provided context.)", "context_used": 5}, {"question_id": "2", "question_text": "Which actresses featured in the movie 'Dhoom 2'?", "question_type": "multiple_choice_multi", "options": ["A) <PERSON><PERSON><PERSON><PERSON>", "B) <PERSON><PERSON><PERSON>", "C) <PERSON><PERSON><PERSON><PERSON>", "D) <PERSON><PERSON>"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": "Answers: A, <PERSON> (Both <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> are listed as actresses in the movie 'Dhoom 2' in the provided context.)", "context_used": 5}, {"question_id": "3", "question_text": "Which films were directed by <PERSON><PERSON>?", "question_type": "multiple_choice_multi", "options": ["A) <PERSON><PERSON>", "B) <PERSON><PERSON><PERSON><PERSON>", "C) My Name Is Khan", "D) <PERSON><PERSON>"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": "Answers: C (The context mentions <PERSON><PERSON> as the director of \"My Name Is Khan\").", "context_used": 5}, {"question_id": "4", "question_text": "Which actors starred in 'Zindagi Na Milegi <PERSON>'?", "question_type": "multiple_choice_multi", "options": ["A) <PERSON><PERSON><PERSON>", "B) <PERSON><PERSON>", "C) <PERSON><PERSON><PERSON>", "D) <PERSON>"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": "Answers: <PERSON>, <PERSON>, <PERSON> (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> are the actors who starred in 'Zindagi Na Milegi Do<PERSON>'. <PERSON><PERSON> did not star in this film.)", "context_used": 1}, {"question_id": "5", "question_text": "Which movies have music by <PERSON><PERSON><PERSON><PERSON>?", "question_type": "multiple_choice_multi", "options": ["A) <PERSON><PERSON>", "B) Slumdog Millionaire", "C) <PERSON><PERSON><PERSON>", "D) 3 Idiots"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": "Answers: A, <PERSON>, <PERSON> (<PERSON><PERSON><PERSON><PERSON> composed the music for <PERSON><PERSON>, Slumdog Millionaire, and <PERSON><PERSON><PERSON>. He did not compose the music for 3 Idiots; <PERSON><PERSON><PERSON> did.)", "context_used": 1}, {"question_id": "6", "question_text": "The movie _____ tells the story of a deaf and mute girl and her teacher.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": "Black", "context_used": 1}, {"question_id": "7", "question_text": "_____ is a film about three engineering students and their college life.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": "3 Idiots", "context_used": 1}, {"question_id": "8", "question_text": "'Rock On!!' is a movie about a _____ band.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": "Rock", "context_used": 1}, {"question_id": "9", "question_text": "_____ is a movie starring <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, dealing with coming-of-age.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": "Wake Up Sid", "context_used": 1}, {"question_id": "10", "question_text": "'Delhi-6' is directed by _____.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": "<PERSON><PERSON><PERSON>", "context_used": 1}, {"question_id": "11", "question_text": "'<PERSON><PERSON><PERSON>' is a fictional story.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": "Answer: True ('<PERSON><PERSON><PERSON>' is a fictional story.)", "context_used": 1}, {"question_id": "12", "question_text": "'My Name Is Khan' deals with the topic of terrorism.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": "Answer: False (While 'My Name Is Khan' touches on themes related to post-9/11 societal attitudes and discrimination, it is primarily a story about a man with Asperger's syndrome who embarks on a journey to meet the President of the United States. The film's central focus is on love, acceptance, and the protagonist's personal journey rather than terrorism itself.)", "context_used": 1}, {"question_id": "13", "question_text": "'Kites' is a movie starring only Indian actors.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": "Answer: <PERSON><PERSON><PERSON> (<PERSON><PERSON> stars both Indian and non-Indian actors. The lead actress is <PERSON>, who is a Mexican actress.)", "context_used": 1}, {"question_id": "14", "question_text": "'Wake Up Sid' is a love story between a college student and an older woman.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": "Answer: False (The context provided states that 'Wake Up Sid' is a love story between a college student and an older woman. However, in the actual film, both characters are young adults close in age, with no significant age gap as suggested by the term \"older woman.\" The film stars <PERSON><PERSON><PERSON> as <PERSON>, a college student, and <PERSON><PERSON><PERSON> as <PERSON><PERSON>, a writer from Kolkata who is not significantly older than <PERSON>.)", "context_used": 1}, {"question_id": "15", "question_text": "'Golmaal' series started in the 2000s.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": "Answer: True (The 'Golmaal' series, directed by <PERSON><PERSON><PERSON>, began with the first film 'Golmaal: Fun Unlimited' released in 2006, which is within the 2000s.)", "context_used": 1}, {"question_id": "match_1", "question_text": "Match the movie with its director:", "question_type": "match_following", "options": ["1. <PERSON><PERSON>", "2. <PERSON><PERSON>", "3. Black Friday", "4. <PERSON><PERSON>", "5. <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "metadata": {"page_number": 3, "confidence": 0.95, "additional_info": "Table format with movies and directors", "extraction_attempt": 1}, "answer": "1. **<PERSON><PERSON><PERSON> (2001)** - **<PERSON><PERSON><PERSON>**\n   - Reasoning: The context clearly states that <PERSON><PERSON><PERSON> is the director of \"Lagaan.\"\n\n2. **<PERSON><PERSON> (2001)** - **<PERSON><PERSON>**\n   - Reasoning: According to the context, <PERSON><PERSON> made his directorial debut with \"Dil Chahta Hai.\"\n\n3. **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (2004)** - **<PERSON><PERSON>**\n   - Reasoning: The context mentions that <PERSON><PERSON> directed \"Veer-Zaara.\"\n\n4. **Chak De! India (2007)** - **<PERSON><PERSON>**\n   - Reasoning: As per the context, <PERSON><PERSON> is the director of \"Chak De! India.\"\n\n5. **Black Friday (2004)** - **<PERSON><PERSON><PERSON>**\n   - Reasoning: The context states that <PERSON><PERSON><PERSON> directed \"Black Friday.\"\n\nThese matches are based solely on the information provided in the context.", "context_used": 5}, {"question_id": "short_1", "question_text": "Name one movie that explored a social issue in the 2000-2010 decade.", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": "One movie that explored a social issue in the 2000-2010 decade is **\"Rang De <PERSON>\" (2006)**. Directed by <PERSON><PERSON><PERSON>, the film addressed youth apathy and political corruption. It tells the story of a group of disillusioned young Indians who are inspired by the roles they play in a documentary about Indian freedom fighters to confront contemporary corruption and injustice. The film sparked real-life protests and discussions on civic responsibility, highlighting its significant impact on social discourse. \"Rang De Basanti\" won the National Film Award for Best Popular Film Providing Wholesome Entertainment, underscoring its relevance and influence.", "context_used": 5}, {"question_id": "short_2", "question_text": "What is the significance of the Eiffel Tower scene in '<PERSON><PERSON><PERSON>'?", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": "The Eiffel Tower scene in '<PERSON><PERSON><PERSON>' is a pivotal moment in the film where the two protagonists, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, meet after embarking on a journey to fulfill their last wishes before planning to end their lives. This scene symbolizes a shared vulnerability between the two characters, as they open up to each other about their struggles and despair. It marks a turning point in their relationship and their individual journies, as they make a pact to embrace life, even if temporarily. The Eiffel Tower, being an iconic symbol of love and romance, serves as a poignant backdrop for this new beginning, shifting their mindset from despair to a glimmer of hope. This moment encapsulates the themes of self-discovery, second chances, and the blossoming of love between <PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "context_used": 5}, {"question_id": "short_3", "question_text": "Name a comedy film starring <PERSON><PERSON><PERSON> from this decade.", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": "The provided context does not include any information about comedy films starring <PERSON><PERSON><PERSON> from the 2000-2010 decade. Therefore, I cannot name a comedy film starring <PERSON><PERSON><PERSON> from this period based on the given information.", "context_used": 5}, {"question_id": "short_4", "question_text": "Which movie is known for the dialogue 'All is well'?", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": "The movie known for the dialogue 'All is well' is *3 Idiots*, directed by <PERSON><PERSON> and released in 2009. This phrase is a mantra for self-reassurance used by the character <PERSON>, played by <PERSON><PERSON><PERSON>, and it became a popular catchphrase in Indian culture.", "context_used": 5}, {"question_id": "short_5", "question_text": "Name one animated film from this decade.", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": "The context provided does not contain enough information to name an animated film from the decade 2000-2010. Therefore, I cannot accurately answer the question based on the given context.", "context_used": 1}], "answered_questions": 29, "rag_processing_complete": true}