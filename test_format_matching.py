#!/usr/bin/env python3
"""
Test script to verify format-matching answer functionality.
"""
import json
import logging
from pathlib import Path
from typing import Dict, Any

from config import QUESTION_TYPES
from utils.pdf_generator import generate_answer_pdf

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_questions() -> Dict[str, Any]:
    """Create test questions with different types to verify format matching."""
    
    test_questions = {
        "total_questions": 6,
        "extraction_timestamp": 1703097600,  # 2023-12-20
        "rag_processing_complete": True,
        "answered_questions": 6,
        "questions": [
            {
                "question_id": "test_mcq_single",
                "question_text": "Which actor starred in the movie <PERSON><PERSON><PERSON>?",
                "question_type": QUESTION_TYPES.MULTIPLE_CHOICE_SINGLE,
                "options": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
                "answer": "<PERSON><PERSON> <PERSON> ✓  <PERSON><PERSON>",
                "context_used": 3,
                "metadata": {"page_number": 1, "confidence": 0.95}
            },
            {
                "question_id": "test_mcq_multi",
                "question_text": "Which of the following movies won National Film Awards? (Select all that apply)",
                "question_type": QUESTION_TYPES.MULTIPLE_CHOICE_MULTI,
                "options": ["Lagaan", "Koi... Mil Gaya", "Taare Zameen Par", "Dangal"],
                "answer": "A. Lagaan ✓  B. Koi... Mil Gaya  C. Taare Zameen Par ✓  D. Dangal",
                "context_used": 4,
                "metadata": {"page_number": 1, "confidence": 0.88}
            },
            {
                "question_id": "test_true_false",
                "question_text": "Lagaan was nominated for an Academy Award.",
                "question_type": QUESTION_TYPES.TRUE_FALSE,
                "options": None,
                "answer": "True ✓  False",
                "context_used": 2,
                "metadata": {"page_number": 2, "confidence": 0.92}
            },
            {
                "question_id": "test_fill_blank",
                "question_text": "The movie _____ was directed by Ashutosh Gowariker and starred _____ in the lead role.",
                "question_type": QUESTION_TYPES.FILL_IN_BLANK,
                "options": None,
                "answer": "The movie Lagaan was directed by Ashutosh Gowariker and starred Aamir Khan in the lead role.",
                "context_used": 3,
                "metadata": {"page_number": 2, "confidence": 0.90}
            },
            {
                "question_id": "test_checkbox",
                "question_text": "Select the characteristics of Bollywood movies in the 2000s:",
                "question_type": QUESTION_TYPES.CHECKBOX,
                "options": ["Musical numbers", "Family drama themes", "International recognition", "Black and white cinematography"],
                "answer": "☑ Musical numbers  ☑ Family drama themes  ☑ International recognition  ☐ Black and white cinematography",
                "context_used": 5,
                "metadata": {"page_number": 3, "confidence": 0.85}
            },
            {
                "question_id": "test_match_following",
                "question_text": "Match the directors with their famous movies:",
                "question_type": QUESTION_TYPES.MATCH_FOLLOWING,
                "options": None,
                "answer": "1. Ashutosh Gowariker → Lagaan  2. Rajkumar Hirani → 3 Idiots  3. Karan Johar → Kabhi Khushi Kabhie Gham",
                "context_used": 4,
                "metadata": {"page_number": 3, "confidence": 0.87}
            }
        ]
    }
    
    return test_questions

def test_format_matching():
    """Test the format-matching functionality."""
    logger.info("🧪 Testing format-matching answer functionality...")
    
    # Create test questions
    test_data = create_test_questions()
    
    # Save test data as JSON for inspection
    json_output_path = "test_format_matching_questions.json"
    with open(json_output_path, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    logger.info(f"📄 Test questions saved to: {json_output_path}")
    
    # Generate PDF with format-matched answers
    pdf_output_path = "test_format_matching_answers.pdf"
    success = generate_answer_pdf(test_data, pdf_output_path)
    
    if success:
        logger.info(f"✅ Format-matching PDF generated successfully: {pdf_output_path}")
        logger.info("📋 Test Results Summary:")
        logger.info("  • MCQ Single: Shows all options with correct one marked ✓")
        logger.info("  • MCQ Multi: Shows all options with correct ones marked ✓")
        logger.info("  • True/False: Shows both options with correct one marked ✓")
        logger.info("  • Fill-in-blank: Shows complete sentence with blanks filled")
        logger.info("  • Checkbox: Shows options with ☑ for correct and ☐ for incorrect")
        logger.info("  • Match-following: Shows matched pairs with arrows")
        
        # Verify files exist
        if Path(pdf_output_path).exists():
            logger.info(f"📊 PDF file size: {Path(pdf_output_path).stat().st_size} bytes")
        if Path(json_output_path).exists():
            logger.info(f"📊 JSON file size: {Path(json_output_path).stat().st_size} bytes")
            
    else:
        logger.error("❌ Failed to generate format-matching PDF")
        return False
    
    logger.info("🎉 Format-matching test completed successfully!")
    return True

if __name__ == "__main__":
    test_format_matching()
