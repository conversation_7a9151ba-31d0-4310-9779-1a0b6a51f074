{"total_questions": 29, "extraction_timestamp": 1748292440.2412856, "questions": [{"question_id": "1", "question_text": "Which movie features the song \"Jai Ho\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON>", "B) Slumdog Millionaire", "C) <PERSON>b <PERSON>", "D) 3 Idiots"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": null}, {"question_id": "2", "question_text": "Who directed the film \"<PERSON>ng <PERSON>\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON>", "B) <PERSON><PERSON><PERSON>", "C) <PERSON><PERSON><PERSON>", "D) <PERSON><PERSON>"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": null}, {"question_id": "3", "question_text": "Which actor played the lead role in the movie \"Ghajin<PERSON>\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON>", "B) <PERSON><PERSON><PERSON>", "C) <PERSON><PERSON>", "D) <PERSON><PERSON><PERSON>"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": null}, {"question_id": "4", "question_text": "Which film features the song \"Mauja Hi Mauja\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON>", "B) <PERSON> <PERSON><PERSON>", "C) Bach<PERSON>", "D) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": null}, {"question_id": "5", "question_text": "Which movie is known for the dialogue \"Mere paas maa hai\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON>", "B) <PERSON><PERSON><PERSON><PERSON>", "C) Waqt", "D) The dialogue is not from the 2000-2010 decade."], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": null}, {"question_id": "6", "question_text": "Which film marked the debut of <PERSON><PERSON><PERSON>?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON><PERSON>", "B) Wake Up Sid", "C) <PERSON><PERSON><PERSON>", "D) <PERSON>: Salesman of the Year"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": null}, {"question_id": "7", "question_text": "Which movie features the character \"<PERSON>\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON> M.B.B.S.", "B) Lage <PERSON>", "C) 3 Idiots", "D) PK"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": null}, {"question_id": "8", "question_text": "Which actress played the lead role in \"Fashion\"?", "question_type": "multiple_choice_single", "options": ["A) <PERSON><PERSON><PERSON>", "B) <PERSON>", "C) <PERSON><PERSON><PERSON>", "D) <PERSON><PERSON>"], "metadata": {"page_number": 1, "confidence": 1.0, "additional_info": "Single Answer Multiple Choice Question", "extraction_attempt": 1}, "answer": null}, {"question_id": "1", "question_text": "Which of the following movies starred <PERSON><PERSON><PERSON><PERSON> in the 2000-2010 decade?", "question_type": "multiple_choice_multi", "options": ["A) Black", "B) Sarkar", "C) Paa", "D) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": null}, {"question_id": "2", "question_text": "Which actresses featured in the movie 'Dhoom 2'?", "question_type": "multiple_choice_multi", "options": ["A) <PERSON><PERSON><PERSON><PERSON>", "B) <PERSON><PERSON><PERSON>", "C) <PERSON><PERSON><PERSON><PERSON>", "D) <PERSON><PERSON>"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": null}, {"question_id": "3", "question_text": "Which films were directed by <PERSON><PERSON>?", "question_type": "multiple_choice_multi", "options": ["A) <PERSON><PERSON>", "B) <PERSON><PERSON><PERSON><PERSON>", "C) My Name Is Khan", "D) <PERSON><PERSON>"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": null}, {"question_id": "4", "question_text": "Which actors starred in 'Zindagi Na Milegi <PERSON>'?", "question_type": "multiple_choice_multi", "options": ["A) <PERSON><PERSON><PERSON>", "B) <PERSON><PERSON>", "C) <PERSON><PERSON><PERSON>", "D) <PERSON>"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": null}, {"question_id": "5", "question_text": "Which movies have music by <PERSON><PERSON><PERSON><PERSON>?", "question_type": "multiple_choice_multi", "options": ["A) <PERSON><PERSON>", "B) Slumdog Millionaire", "C) <PERSON><PERSON><PERSON>", "D) 3 Idiots"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Multiple Answer Multiple Choice Questions", "extraction_attempt": 1}, "answer": null}, {"question_id": "6", "question_text": "The movie _____ tells the story of a deaf and mute girl and her teacher.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": null}, {"question_id": "7", "question_text": "_____ is a film about three engineering students and their college life.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": null}, {"question_id": "8", "question_text": "'Rock On!!' is a movie about a _____ band.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": null}, {"question_id": "9", "question_text": "_____ is a movie starring <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, dealing with coming-of-age.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": null}, {"question_id": "10", "question_text": "'Delhi-6' is directed by _____.", "question_type": "fill_in_blank", "options": null, "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "Fill in the Blanks", "extraction_attempt": 1}, "answer": null}, {"question_id": "11", "question_text": "'<PERSON><PERSON><PERSON>' is a fictional story.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": null}, {"question_id": "12", "question_text": "'My Name Is Khan' deals with the topic of terrorism.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": null}, {"question_id": "13", "question_text": "'Kites' is a movie starring only Indian actors.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": null}, {"question_id": "14", "question_text": "'Wake Up Sid' is a love story between a college student and an older woman.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": null}, {"question_id": "15", "question_text": "'Golmaal' series started in the 2000s.", "question_type": "true_false", "options": ["True", "False"], "metadata": {"page_number": 2, "confidence": 1.0, "additional_info": "True or False", "extraction_attempt": 1}, "answer": null}, {"question_id": "match_1", "question_text": "Match the movie with its director:", "question_type": "match_following", "options": ["1. <PERSON><PERSON>", "2. <PERSON><PERSON>", "3. Black Friday", "4. <PERSON><PERSON>", "5. <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "metadata": {"page_number": 3, "confidence": 0.95, "additional_info": "Table format with movies and directors", "extraction_attempt": 1}, "answer": null}, {"question_id": "short_1", "question_text": "Name one movie that explored a social issue in the 2000-2010 decade.", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": null}, {"question_id": "short_2", "question_text": "What is the significance of the Eiffel Tower scene in '<PERSON><PERSON><PERSON>'?", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": null}, {"question_id": "short_3", "question_text": "Name a comedy film starring <PERSON><PERSON><PERSON> from this decade.", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": null}, {"question_id": "short_4", "question_text": "Which movie is known for the dialogue 'All is well'?", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": null}, {"question_id": "short_5", "question_text": "Name one animated film from this decade.", "question_type": "textual_answer", "options": null, "metadata": {"page_number": 3, "confidence": 0.98, "additional_info": "Short answer question", "extraction_attempt": 1}, "answer": null}]}