"""
Debug script to understand Cohere API response format.
"""
import cohere
from config import API_CONFIG

def debug_cohere_response():
    """Debug Cohere API response format."""
    try:
        client = cohere.ClientV2(api_key=API_CONFIG.cohere_api_key)

        # Test embedding
        response = client.embed(
            texts=["test text"],
            model=API_CONFIG.cohere_embed_model,
            input_type="search_document",
            embedding_types=["float"]
        )

        print("Response type:", type(response))
        print("Embeddings type:", type(response.embeddings))
        print("Embeddings attributes:", dir(response.embeddings))

        # Check if embeddings has float attribute
        if hasattr(response.embeddings, 'float'):
            print("Has 'float' attribute")
            float_embeddings = response.embeddings.float
            print("Float embeddings type:", type(float_embeddings))
            print("Float embeddings length:", len(float_embeddings))
            print("First float embedding type:", type(float_embeddings[0]))
            print("First float embedding length:", len(float_embeddings[0]))
            print("First 5 values:", float_embeddings[0][:5])
        else:
            print("No 'float' attribute")

        # Try other possible attributes
        for attr in ['values', 'data', 'embedding']:
            if hasattr(response.embeddings, attr):
                print(f"Has '{attr}' attribute")
                attr_value = getattr(response.embeddings, attr)
                print(f"{attr} type:", type(attr_value))
                if hasattr(attr_value, '__len__'):
                    print(f"{attr} length:", len(attr_value))

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cohere_response()
